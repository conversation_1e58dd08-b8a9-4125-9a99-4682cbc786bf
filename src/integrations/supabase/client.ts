// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://tsevgzikucpqgsgeethx.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRzZXZnemlrdWNwcWdzZ2VldGh4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTY3NDgxMDUsImV4cCI6MjA3MjMyNDEwNX0.jfjoR186JL_NoI7EeJV2SNdK3uy-EUCu8q0QYzcWnNU";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});