import { useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Wand2 } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { HeroSelection } from "@/components/HeroSelection";
import { PersonalizationForm } from "@/components/PersonalizationForm";
import { LetterPreview } from "@/components/LetterPreview";
import { Paywall } from "@/components/Paywall";
import heroesBanner from "@/assets/heroes-banner.jpg";

interface ChildData {
  name: string;
  achievement: string;
  values: string[];
}

interface Hero {
  id: string;
  name: string;
  description: string;
  image: string;
  color: string;
}

const Index = () => {
  const [currentStep, setCurrentStep] = useState<'hero' | 'personalize' | 'preview' | 'paywall'>('hero');
  const [selectedHero, setSelectedHero] = useState<Hero | null>(null);
  const [childData, setChildData] = useState<ChildData | null>(null);

  const handleHeroSelect = (hero: Hero) => {
    setSelectedHero(hero);
    setCurrentStep('personalize');
  };

  const handlePersonalizationComplete = (data: ChildData) => {
    setChildData(data);
    setCurrentStep('paywall');
  };

  const handleBack = () => {
    if (currentStep === 'personalize') {
      setCurrentStep('hero');
    } else if (currentStep === 'preview') {
      setCurrentStep('paywall');
    } else if (currentStep === 'paywall') {
      setCurrentStep('personalize');
    }
  };

  const handlePreviewLetter = () => {
    setCurrentStep('preview');
  };

  return (
    <div className="min-h-screen bg-gradient-subtle">
      {/* Magical Header */}
      <header className="relative overflow-hidden bg-gradient-hero py-16 px-4">
        <div className="absolute inset-0 opacity-30">
          <img 
            src={heroesBanner}
            alt="Magical heroes"
            className="w-full h-full object-cover"
          />
        </div>
        <div className="relative z-10 max-w-4xl mx-auto text-center">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Sparkles className="w-8 h-8 text-primary-foreground animate-sparkle" />
            <h1 className="text-5xl font-bold text-primary-foreground">
              HeroMail
            </h1>
            <Sparkles className="w-8 h-8 text-primary-foreground animate-sparkle" />
          </div>
          <p className="text-xl text-primary-foreground/90 mb-8 max-w-2xl mx-auto">
            Turn your child's achievements into magical letters from their favorite heroes
          </p>
          
          {/* Progress Indicators */}
          <div className="flex items-center justify-center gap-4 mb-8">
            <div className={`flex items-center gap-2 px-4 py-2 rounded-full transition-all duration-300 ${
              currentStep === 'hero' ? 'bg-white/20 text-primary-foreground' : 'bg-white/10 text-primary-foreground/60'
            }`}>
              <Star className="w-4 h-4" />
              <span className="font-medium">Choose Hero</span>
            </div>
            <div className={`flex items-center gap-2 px-4 py-2 rounded-full transition-all duration-300 ${
              currentStep === 'personalize' ? 'bg-white/20 text-primary-foreground' : 'bg-white/10 text-primary-foreground/60'
            }`}>
              <Heart className="w-4 h-4" />
              <span className="font-medium">Personalize</span>
            </div>
            <div className={`flex items-center gap-2 px-4 py-2 rounded-full transition-all duration-300 ${
              currentStep === 'paywall' ? 'bg-white/20 text-primary-foreground' : 'bg-white/10 text-primary-foreground/60'
            }`}>
              <Wand2 className="w-4 h-4" />
              <span className="font-medium">Choose Plan</span>
            </div>
            <div className={`flex items-center gap-2 px-4 py-2 rounded-full transition-all duration-300 ${
              currentStep === 'preview' ? 'bg-white/20 text-primary-foreground' : 'bg-white/10 text-primary-foreground/60'
            }`}>
              <Sparkles className="w-4 h-4" />
              <span className="font-medium">Magic Letter</span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-4 py-12">
        {currentStep === 'hero' && (
          <HeroSelection onHeroSelect={handleHeroSelect} />
        )}
        
        {currentStep === 'personalize' && selectedHero && (
          <PersonalizationForm
            hero={selectedHero}
            onComplete={handlePersonalizationComplete}
            onBack={handleBack}
          />
        )}
        
        {currentStep === 'paywall' && selectedHero && childData && (
          <div className="space-y-8">
            <div className="text-center">
              <Button 
                onClick={handlePreviewLetter}
                variant="outline" 
                className="mb-8"
              >
                Preview Your Letter First
              </Button>
            </div>
            <Paywall
              onBack={handleBack}
              childName={childData.name}
            />
          </div>
        )}
        
        {currentStep === 'preview' && selectedHero && childData && (
          <LetterPreview
            hero={selectedHero}
            childData={childData}
            onBack={handleBack}
          />
        )}
      </main>

      {/* Magical Footer */}
      <footer className="mt-20 py-12 bg-card/50 border-t border-border/30">
        <div className="max-w-4xl mx-auto text-center px-4">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Sparkles className="w-5 h-5 text-primary floating-animation" />
            <p className="text-muted-foreground">
              Creating magical moments, one letter at a time
            </p>
            <Sparkles className="w-5 h-5 text-primary floating-animation" style={{animationDelay: '1s'}} />
          </div>
          <p className="text-sm text-muted-foreground/70">
            © 2024 HeroMail. Inspiring children through heroic stories.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Index;