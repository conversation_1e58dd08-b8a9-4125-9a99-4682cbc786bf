import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";
import { Check, Star, Heart, Shield, Sparkles, BookOpen, Users, Trophy, Mail, Gift, Smile, MessageCircle, ArrowRight, PlayCircle } from "lucide-react";
import { Link } from "react-router-dom";
import { toast } from "sonner";
import heroesBanner from "@/assets/heroes-banner.jpg";

const Landing = () => {
  const [email, setEmail] = useState("");
  const [isSubscribed, setIsSubscribed] = useState(false);

  const handleNewsletterSignup = (e: React.FormEvent) => {
    e.preventDefault();
    if (email) {
      setIsSubscribed(true);
      toast.success("Thank you for subscribing! We'll keep you updated on new features.");
      setEmail("");
    }
  };

  const pricingPlans = [
    {
      name: "One-Time Tryout",
      price: 4.99,
      yearlyPrice: null,
      description: "Perfect for trying out HeroMail",
      features: ["1 personalized letter", "Choose any hero", "High-quality printing", "Free shipping"],
      popular: false,
      cta: "Try Once"
    },
    {
      name: "Monthly Explorer",
      price: 24.99,
      yearlyPrice: 21.24,
      description: "4 magical letters per month",
      features: ["4 letters per month", "All heroes available", "Premium paper quality", "Priority shipping", "Value tracking"],
      popular: true,
      cta: "Start Exploring"
    },
    {
      name: "Monthly Champion",
      price: 49.99,
      yearlyPrice: 42.49,
      description: "8 magical letters per month",
      features: ["8 letters per month", "All heroes + exclusive ones", "Deluxe presentation", "Express shipping", "Progress reports", "Custom hero requests"],
      popular: false,
      cta: "Become Champion"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-subtle">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-hero py-20 px-4">
        <div className="absolute inset-0 opacity-20">
          <img 
            src={heroesBanner}
            alt="Magical heroes"
            className="w-full h-full object-cover"
          />
        </div>
        <div className="relative z-10 max-w-6xl mx-auto text-center">
          <div className="flex items-center justify-center gap-2 mb-6">
            <Sparkles className="w-10 h-10 text-primary-foreground animate-sparkle" />
            <h1 className="text-6xl font-bold text-primary-foreground">
              HeroMail
            </h1>
            <Sparkles className="w-10 h-10 text-primary-foreground animate-sparkle" />
          </div>
          <p className="text-2xl text-primary-foreground/90 mb-8 max-w-3xl mx-auto leading-relaxed">
            Transform your child's achievements into magical letters from their favorite heroes. 
            The first-of-its-kind experience that inspires character building beyond screens.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/create">
              <Button size="lg" className="text-lg px-8 py-6 bg-white text-primary hover:bg-white/90 shadow-magical">
                Start Creating Magic ✨
              </Button>
            </Link>
            <Button variant="outline" size="lg" className="text-lg px-8 py-6 border-white/30 text-primary-foreground hover:bg-white/10">
              See How It Works
            </Button>
          </div>
        </div>
      </section>

      {/* Why Kids Love HeroMail Section */}
      <section className="py-20 px-4 bg-muted/20">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-4">
              Why Kids Can't Stop Talking About HeroMail
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Over 100 families have discovered the magic. Here's what makes children light up with excitement.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card className="text-center p-6 shadow-hero-card border-border/50 hover-scale">
              <CardContent className="pt-4">
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-magical rounded-full flex items-center justify-center">
                  <Gift className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-foreground mb-3">Real Mail Excitement</h3>
                <p className="text-muted-foreground">
                  "Mom, there's a letter for ME!" The thrill of receiving real mail addressed to them personally.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center p-6 shadow-hero-card border-border/50 hover-scale">
              <CardContent className="pt-4">
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-magical rounded-full flex items-center justify-center">
                  <Smile className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-foreground mb-3">Their Hero Knows Them</h3>
                <p className="text-muted-foreground">
                  Personalized letters that mention their specific achievements make them feel truly special.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center p-6 shadow-hero-card border-border/50 hover-scale">
              <CardContent className="pt-4">
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-magical rounded-full flex items-center justify-center">
                  <Star className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-foreground mb-3">Collectible Treasures</h3>
                <p className="text-muted-foreground">
                  Beautiful letters they can keep, display, and treasure forever - not just another notification.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center p-6 shadow-hero-card border-border/50 hover-scale">
              <CardContent className="pt-4">
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-magical rounded-full flex items-center justify-center">
                  <BookOpen className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-foreground mb-3">Story Adventures</h3>
                <p className="text-muted-foreground">
                  Each letter tells a mini-adventure that connects their real life to their hero's world.
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="text-center mt-12">
            <Badge className="bg-primary/10 text-primary text-lg px-6 py-2">
              ✨ Tested and Loved by 100+ Families Nationwide
            </Badge>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-4">
              How the Magic Happens
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              From achievement to heroic letter in just 3 simple steps. Your child receives their personalized letter within 5-7 business days.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 mb-16">
            <div className="text-center">
              <div className="relative">
                <div className="w-20 h-20 mx-auto mb-6 bg-gradient-hero rounded-full flex items-center justify-center shadow-magical">
                  <span className="text-3xl font-bold text-primary-foreground">1</span>
                </div>
                <div className="hidden md:block absolute top-10 left-full w-full h-0.5 bg-gradient-to-r from-primary/50 to-transparent -translate-y-1/2"></div>
              </div>
              <h3 className="text-2xl font-bold text-foreground mb-4">Choose Your Hero</h3>
              <p className="text-muted-foreground">
                Select from our collection of beloved heroes - wizards, knights, fairies, dragons, and more. Each with their unique personality and wisdom.
              </p>
            </div>

            <div className="text-center">
              <div className="relative">
                <div className="w-20 h-20 mx-auto mb-6 bg-gradient-hero rounded-full flex items-center justify-center shadow-magical">
                  <span className="text-3xl font-bold text-primary-foreground">2</span>
                </div>
                <div className="hidden md:block absolute top-10 left-full w-full h-0.5 bg-gradient-to-r from-primary/50 to-transparent -translate-y-1/2"></div>
              </div>
              <h3 className="text-2xl font-bold text-foreground mb-4">Share the Achievement</h3>
              <p className="text-muted-foreground">
                Tell us about your child's accomplishment - big or small. From learning to tie shoes to acts of kindness, every achievement matters.
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 mx-auto mb-6 bg-gradient-hero rounded-full flex items-center justify-center shadow-magical">
                <span className="text-3xl font-bold text-primary-foreground">3</span>
              </div>
              <h3 className="text-2xl font-bold text-foreground mb-4">Magic Delivered</h3>
              <p className="text-muted-foreground">
                Our AI crafts a personalized letter in your hero's voice, professionally printed on premium paper and mailed directly to your child.
              </p>
            </div>
          </div>

          <div className="bg-gradient-hero rounded-2xl p-8 text-center shadow-magical">
            <div className="max-w-3xl mx-auto">
              <PlayCircle className="w-16 h-16 text-primary-foreground mx-auto mb-4" />
              <h3 className="text-3xl font-bold text-primary-foreground mb-4">See It In Action</h3>
              <p className="text-xl text-primary-foreground/90 mb-6">
                Watch how Emma's face lit up when she received her first letter from the Brave Knight congratulating her on learning to ride her bike.
              </p>
              <Button size="lg" className="bg-white text-primary hover:bg-white/90">
                <PlayCircle className="w-5 h-5 mr-2" />
                Watch Demo Video
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Impact Section */}
      <section className="py-20 px-4 bg-muted/20">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-4">
              Why HeroMail Transforms Your Child's Growth
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              In a world dominated by screens, HeroMail brings back the magic of tangible, personal recognition 
              that builds character and lasting memories.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 mb-16">
            <Card className="text-center p-8 shadow-hero-card border-border/50">
              <CardHeader>
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-magical rounded-full flex items-center justify-center">
                  <BookOpen className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-2xl">Character Building</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Reinforce positive values like courage, kindness, and perseverance through personalized storytelling 
                  that resonates with your child's achievements.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center p-8 shadow-hero-card border-border/50">
              <CardHeader>
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-magical rounded-full flex items-center justify-center">
                  <Heart className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-2xl">Emotional Connection</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Create lasting emotional bonds as your child receives physical letters that validate their efforts 
                  and inspire them to continue growing.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center p-8 shadow-hero-card border-border/50">
              <CardHeader>
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-magical rounded-full flex items-center justify-center">
                  <Shield className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-2xl">Screen-Free Magic</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Give your child something to treasure beyond digital devices - tangible letters they can hold, 
                  read, and cherish for years to come.
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Benefits Grid */}
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-3xl font-bold text-foreground mb-6">
                The First-of-Its-Kind Experience
              </h3>
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <Trophy className="w-6 h-6 text-primary mt-1" />
                  <div>
                    <h4 className="font-semibold text-foreground">Achievement Recognition</h4>
                    <p className="text-muted-foreground">Celebrate every milestone, big or small, with personalized recognition.</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <Users className="w-6 h-6 text-primary mt-1" />
                  <div>
                    <h4 className="font-semibold text-foreground">Family Bonding</h4>
                    <p className="text-muted-foreground">Create shared moments of joy and pride as your child reads their heroic letters.</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <Star className="w-6 h-6 text-primary mt-1" />
                  <div>
                    <h4 className="font-semibold text-foreground">Lasting Impact</h4>
                    <p className="text-muted-foreground">Build a collection of achievements that your child will treasure forever.</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-gradient-hero rounded-2xl p-8 text-center shadow-magical">
              <div className="text-6xl mb-4">📮</div>
              <h4 className="text-2xl font-bold text-primary-foreground mb-2">Physical Letters</h4>
              <p className="text-primary-foreground/90">
                Real, tangible letters that arrive in your mailbox, creating anticipation and excitement 
                that digital messages simply cannot match.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-4">
              What Families Are Saying
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Real stories from parents and grandparents who've witnessed the magic firsthand
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 mb-16">
            <Card className="shadow-hero-card border-border/50">
              <CardContent className="p-8 text-center">
                <div className="flex justify-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 fill-primary text-primary" />
                  ))}
                </div>
                <blockquote className="text-lg text-muted-foreground mb-6 italic">
                  "My 7-year-old daughter has kept every single HeroMail letter in a special box by her bed. She reads them when she's feeling scared or needs encouragement. It's incredible how much these letters mean to her - they've actually helped her become more confident and kind."
                </blockquote>
                <div className="flex items-center justify-center gap-4">
                  <div className="w-12 h-12 bg-gradient-magical rounded-full flex items-center justify-center">
                    <span className="text-white font-bold">S</span>
                  </div>
                  <div className="text-left">
                    <p className="font-semibold text-foreground">Sarah Martinez</p>
                    <p className="text-sm text-muted-foreground">Mother of Emma, age 7</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-hero-card border-border/50">
              <CardContent className="p-8 text-center">
                <div className="flex justify-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 fill-primary text-primary" />
                  ))}
                </div>
                <blockquote className="text-lg text-muted-foreground mb-6 italic">
                  "As a grandmother, I love sending HeroMail letters to my grandson. The excitement in his voice when he calls to tell me about his letter from the Wise Wizard is priceless. It's created such a special bond between us, and I love how the letters reinforce the values we're trying to teach him."
                </blockquote>
                <div className="flex items-center justify-center gap-4">
                  <div className="w-12 h-12 bg-gradient-magical rounded-full flex items-center justify-center">
                    <span className="text-white font-bold">M</span>
                  </div>
                  <div className="text-left">
                    <p className="font-semibold text-foreground">Margaret Johnson</p>
                    <p className="text-sm text-muted-foreground">Grandmother of Tyler, age 9</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-hero-card border-border/50">
              <CardContent className="p-8 text-center">
                <div className="flex justify-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 fill-primary text-primary" />
                  ))}
                </div>
                <blockquote className="text-lg text-muted-foreground mb-6 italic">
                  "We've been using HeroMail for 6 months now, and it's transformed how our kids view their achievements. Instead of just moving on to the next thing, they take time to feel proud of what they've accomplished. The letters have become family treasures that we'll keep forever."
                </blockquote>
                <div className="flex items-center justify-center gap-4">
                  <div className="w-12 h-12 bg-gradient-magical rounded-full flex items-center justify-center">
                    <span className="text-white font-bold">R</span>
                  </div>
                  <div className="text-left">
                    <p className="font-semibold text-foreground">Robert Chen</p>
                    <p className="text-sm text-muted-foreground">Father of Lily & Alex, ages 6 & 8</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="text-center mt-12">
            <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <div className="flex flex-col items-center gap-2">
                <div className="text-4xl font-bold text-primary">2,450+</div>
                <div className="flex items-center gap-2 text-lg font-medium text-muted-foreground">
                  <MessageCircle className="w-5 h-5" />
                  <span>Happy Families</span>
                </div>
              </div>
              <div className="flex flex-col items-center gap-2">
                <div className="text-4xl font-bold text-primary">4.9/5</div>
                <div className="flex items-center gap-2 text-lg font-medium text-muted-foreground">
                  <Star className="w-5 h-5 fill-primary text-primary" />
                  <span>Average Rating</span>
                </div>
              </div>
              <div className="flex flex-col items-center gap-2">
                <div className="text-4xl font-bold text-primary">98%</div>
                <div className="flex items-center gap-2 text-lg font-medium text-muted-foreground">
                  <Heart className="w-5 h-5" />
                  <span>Would Recommend</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 px-4 bg-muted/30">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-4">
              Choose Your Adventure
            </h2>
            <p className="text-xl text-muted-foreground">
              Start with a single letter or build a magical tradition with our monthly plans
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {pricingPlans.map((plan, index) => (
              <Card key={index} className={`relative shadow-hero-card ${plan.popular ? 'border-primary border-2' : 'border-border/50'}`}>
                {plan.popular && (
                  <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-primary text-primary-foreground">
                    Most Popular
                  </Badge>
                )}
                <CardHeader className="text-center pb-4">
                  <CardTitle className="text-2xl">{plan.name}</CardTitle>
                  <div className="space-y-2">
                    <div className="text-4xl font-bold text-primary">
                      ${plan.price}
                      {plan.name !== "One-Time Tryout" && <span className="text-lg text-muted-foreground">/month</span>}
                    </div>
                    {plan.yearlyPrice && (
                      <div className="text-sm text-muted-foreground">
                        <span className="line-through">${(plan.price * 12).toFixed(2)}</span> 
                        <span className="text-primary font-semibold ml-2">
                          ${(plan.yearlyPrice * 12).toFixed(2)}/year
                        </span>
                        <Badge variant="secondary" className="ml-2 text-xs">15% OFF</Badge>
                      </div>
                    )}
                  </div>
                  <p className="text-muted-foreground">{plan.description}</p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ul className="space-y-3">
                    {plan.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center gap-2">
                        <Check className="w-5 h-5 text-primary" />
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Link to="/create" className="block">
                    <Button className="w-full mt-6" variant={plan.popular ? "default" : "outline"}>
                      {plan.cta}
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-12">
            <p className="text-muted-foreground mb-4">
              All plans include free shipping and our happiness guarantee
            </p>
            <div className="flex items-center justify-center gap-6 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <Shield className="w-4 h-4" />
                <span>Secure Payment</span>
              </div>
              <div className="flex items-center gap-2">
                <Heart className="w-4 h-4" />
                <span>30-Day Guarantee</span>
              </div>
              <div className="flex items-center gap-2">
                <Sparkles className="w-4 h-4" />
                <span>Cancel Anytime</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-16 px-4 bg-gradient-hero">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-primary-foreground mb-4">
            Stay Updated on New Heroes & Features
          </h2>
          <p className="text-xl text-primary-foreground/90 mb-8">
            Be the first to know about new heroes, seasonal letters, and special features
          </p>
          
          {!isSubscribed ? (
            <form onSubmit={handleNewsletterSignup} className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <Input
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="flex-1 bg-white/10 border-white/20 text-primary-foreground placeholder:text-primary-foreground/70"
                required
              />
              <Button type="submit" className="bg-white text-primary hover:bg-white/90">
                Subscribe
              </Button>
            </form>
          ) : (
            <div className="bg-white/10 rounded-lg p-6 max-w-md mx-auto">
              <Check className="w-8 h-8 text-primary-foreground mx-auto mb-2" />
              <p className="text-primary-foreground font-semibold">Thank you for subscribing!</p>
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default Landing;