import { useState } from "react";
import { ArrowLeft, <PERSON>rk<PERSON>, <PERSON>, Star, <PERSON>, Crown, Lightbulb } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";

interface Hero {
  id: string;
  name: string;
  description: string;
  image: string;
  color: string;
}

interface ChildData {
  name: string;
  achievement: string;
  values: string[];
}

interface PersonalizationFormProps {
  hero: Hero;
  onComplete: (data: ChildData) => void;
  onBack: () => void;
}

export const PersonalizationForm = ({ hero, onComplete, onBack }: PersonalizationFormProps) => {
  const [childName, setChildName] = useState("");
  const [achievement, setAchievement] = useState("");
  const [selectedValues, setSelectedValues] = useState<string[]>([]);

  const values = [
    { id: 'kindness', label: 'Kindness', icon: Heart },
    { id: 'bravery', label: 'Bravery', icon: Shield },
    { id: 'curiosity', label: 'Curiosity', icon: Lightbulb },
    { id: 'perseverance', label: 'Perseverance', icon: Star },
    { id: 'leadership', label: 'Leadership', icon: Crown },
    { id: 'creativity', label: 'Creativity', icon: Sparkles }
  ];

  const toggleValue = (valueId: string) => {
    setSelectedValues(prev => 
      prev.includes(valueId) 
        ? prev.filter(v => v !== valueId)
        : [...prev, valueId]
    );
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (childName && achievement && selectedValues.length > 0) {
      onComplete({
        name: childName,
        achievement,
        values: selectedValues
      });
    }
  };

  const isFormValid = childName && achievement && selectedValues.length > 0;

  return (
    <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="text-center mb-8">
        <Button 
          variant="ghost" 
          onClick={onBack}
          className="absolute left-4 top-4 hover:bg-muted/50"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        
        <div className="flex items-center justify-center gap-3 mb-4">
          <div className="text-4xl">{hero.image}</div>
          <div>
            <h2 className="text-2xl font-bold text-foreground">
              Tell {hero.name.split(' ').pop()} About Your Child
            </h2>
            <p className="text-muted-foreground">
              {hero.description}
            </p>
          </div>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-8">
        <div className="parchment-card p-8 space-y-6">
          {/* Child's Name */}
          <div className="space-y-3">
            <Label htmlFor="childName" className="text-lg font-semibold text-foreground">
              What's your child's name?
            </Label>
            <Input
              id="childName"
              value={childName}
              onChange={(e) => setChildName(e.target.value)}
              placeholder="Enter your child's name"
              className="text-lg p-4 border-border/50 focus:border-primary"
              required
            />
          </div>

          {/* Achievement */}
          <div className="space-y-3">
            <Label htmlFor="achievement" className="text-lg font-semibold text-foreground">
              What amazing thing did they do?
            </Label>
            <Textarea
              id="achievement"
              value={achievement}
              onChange={(e) => setAchievement(e.target.value)}
              placeholder="Tell us about their achievement, big or small! (e.g., learned to ride a bike, helped a friend, read their first book, scored a goal...)"
              className="min-h-[120px] p-4 border-border/50 focus:border-primary resize-none"
              required
            />
          </div>

          {/* Values Selection */}
          <div className="space-y-4">
            <Label className="text-lg font-semibold text-foreground">
              Which values would you like {hero.name.split(' ').pop()} to celebrate?
            </Label>
            <p className="text-sm text-muted-foreground">
              Choose the qualities you want to encourage in your child
            </p>
            
            <div className="grid grid-cols-2 gap-3">
              {values.map((value) => {
                const IconComponent = value.icon;
                const isSelected = selectedValues.includes(value.id);
                
                return (
                  <button
                    key={value.id}
                    type="button"
                    onClick={() => toggleValue(value.id)}
                    className={`p-4 rounded-lg border-2 transition-all duration-300 text-left ${
                      isSelected
                        ? 'border-primary bg-primary/10 shadow-glow'
                        : 'border-border/30 hover:border-primary/50 hover:bg-muted/50'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <IconComponent className={`w-5 h-5 ${
                        isSelected ? 'text-primary' : 'text-muted-foreground'
                      }`} />
                      <span className={`font-medium ${
                        isSelected ? 'text-primary' : 'text-foreground'
                      }`}>
                        {value.label}
                      </span>
                    </div>
                  </button>
                );
              })}
            </div>
            
            {selectedValues.length === 0 && (
              <p className="text-sm text-muted-foreground italic">
                Please select at least one value to continue
              </p>
            )}
          </div>
        </div>

        {/* Submit Button */}
        <div className="text-center">
          <Button
            type="submit"
            disabled={!isFormValid}
            className={`magical-button text-lg px-8 py-4 ${
              !isFormValid ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            <Sparkles className="w-5 h-5 mr-2" />
            Create Magic Letter
            <Sparkles className="w-5 h-5 ml-2" />
          </Button>
          
          {isFormValid && (
            <p className="text-sm text-muted-foreground mt-3 animate-fade-in">
              ✨ Ready to create something magical!
            </p>
          )}
        </div>
      </form>
    </div>
  );
};