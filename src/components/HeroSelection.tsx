import { <PERSON>, <PERSON>, Sparkles, Heart, Sword } from "lucide-react";
import wizardHero from "@/assets/wizard-hero.jpg";
import knightHero from "@/assets/knight-hero.jpg";
import fairyHero from "@/assets/fairy-hero.jpg";
import dragonHero from "@/assets/dragon-hero.jpg";
import explorerHero from "@/assets/explorer-hero.jpg";
import unicornHero from "@/assets/unicorn-hero.jpg";

interface Hero {
  id: string;
  name: string;
  description: string;
  image: string;
  color: string;
}

interface HeroSelectionProps {
  onHeroSelect: (hero: Hero) => void;
}

export const HeroSelection = ({ onHeroSelect }: HeroSelectionProps) => {
  const heroes: Hero[] = [
    {
      id: 'wizard',
      name: 'Wise Wizard Merlin',
      description: 'Master of wisdom and magical knowledge',
      image: wizard<PERSON>ero,
      color: 'from-primary to-primary-glow'
    },
    {
      id: 'knight',
      name: 'Brave Knight Aria',
      description: 'Protector of courage and honor',
      image: knight<PERSON><PERSON>,
      color: 'from-secondary to-accent'
    },
    {
      id: 'fairy',
      name: 'Magical Fairy Luna',
      description: 'Guardian of kindness and dreams',
      image: fairy<PERSON><PERSON>,
      color: 'from-accent to-primary-glow'
    },
    {
      id: 'dragon',
      name: 'Friendly Dragon Ember',
      description: 'Champion of friendship and adventure',
      image: dragonHero,
      color: 'from-primary-glow to-secondary'
    },
    {
      id: 'explorer',
      name: 'Explorer Captain Nova',
      description: 'Leader in curiosity and discovery',
      image: explorerHero,
      color: 'from-secondary to-primary'
    },
    {
      id: 'unicorn',
      name: 'Mystic Unicorn Star',
      description: 'Symbol of purity and magic',
      image: unicornHero,
      color: 'from-accent to-secondary'
    }
  ];

  const getHeroIcon = (heroId: string) => {
    switch (heroId) {
      case 'wizard': return Crown;
      case 'knight': return Shield;
      case 'fairy': return Sparkles;
      case 'dragon': return Heart;
      case 'explorer': return Sword;
      case 'unicorn': return Sparkles;
      default: return Crown;
    }
  };

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-foreground mb-4">
          Choose Your Child's Hero
        </h2>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Select the magical hero who will write a personalized letter celebrating your child's achievement
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {heroes.map((hero) => {
          const IconComponent = getHeroIcon(hero.id);
          
          return (
            <div
              key={hero.id}
              onClick={() => onHeroSelect(hero)}
              className="hero-card group cursor-pointer transform transition-all duration-300 hover:scale-105"
            >
              <div className="relative overflow-hidden rounded-2xl shadow-hero-card">
                <div className="aspect-[4/5] relative">
                  <img 
                    src={hero.image} 
                    alt={hero.name}
                    className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                  />
                  <div className={`absolute inset-0 bg-gradient-to-t ${hero.color} opacity-60 group-hover:opacity-40 transition-opacity duration-300`} />
                  
                  {/* Hero Icon Overlay */}
                  <div className="absolute top-4 right-4">
                    <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                      <IconComponent className="w-6 h-6 text-white group-hover:animate-sparkle" />
                    </div>
                  </div>
                  
                  {/* Content Overlay */}
                  <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                    <h3 className="text-2xl font-bold mb-2 group-hover:text-primary-glow transition-colors">
                      {hero.name}
                    </h3>
                    <p className="text-white/90 mb-4 group-hover:text-white transition-colors">
                      {hero.description}
                    </p>
                    
                    <div className="opacity-0 group-hover:opacity-100 transform translate-y-4 group-hover:translate-y-0 transition-all duration-300">
                      <div className="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2 inline-flex items-center gap-2">
                        <span className="font-semibold">Choose {hero.name.split(' ')[0]}</span>
                        <Sparkles className="w-4 h-4 animate-sparkle" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      <div className="text-center mt-12">
        <div className="inline-flex items-center gap-2 px-6 py-3 bg-muted/50 rounded-full">
          <Sparkles className="w-5 h-5 text-primary animate-sparkle" />
          <span className="text-muted-foreground">More heroes coming soon!</span>
          <Sparkles className="w-5 h-5 text-primary animate-sparkle" />
        </div>
      </div>
    </div>
  );
};