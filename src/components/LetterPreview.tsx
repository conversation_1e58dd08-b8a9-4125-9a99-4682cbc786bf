import { useState } from "react";
import { <PERSON>Left, <PERSON>rkles, Mail, Heart, Download, Send } from "lucide-react";
import { Button } from "@/components/ui/button";

interface Hero {
  id: string;
  name: string;
  description: string;
  image: string;
  color: string;
}

interface ChildData {
  name: string;
  achievement: string;
  values: string[];
}

interface LetterPreviewProps {
  hero: Hero;
  childData: ChildData;
  onBack: () => void;
}

export const LetterPreview = ({ hero, childData, onBack }: LetterPreviewProps) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [showCheckout, setShowCheckout] = useState(false);

  // Generate a personalized letter based on the hero and child data
  const generateLetter = () => {
    const heroName = hero.name;
    const childName = childData.name;
    const achievement = childData.achievement;
    const values = childData.values;

    const valueMessages = {
      kindness: "Your kindness lights up the world around you",
      bravery: "You showed true courage when it mattered most",
      curiosity: "Your curiosity opens doors to amazing discoveries",
      perseverance: "You never gave up, even when things got tough",
      leadership: "You inspire others with your strong leadership",
      creativity: "Your creativity brings magic to everything you do"
    };

    const selectedValueMessages = values.map(v => valueMessages[v as keyof typeof valueMessages]).join(". ");

    return `Dear ${childName},

Greetings from the realm of heroes! I am ${heroName}, and word of your incredible achievement has reached me across magical lands.

${achievement} - what an amazing accomplishment! I am so proud of you.

${selectedValueMessages}. These are the qualities that make true heroes, and I see them shining brightly in you.

Remember, every great hero started with small brave steps, just like the one you took. Your journey is just beginning, and I have no doubt you will continue to do wonderful things.

Keep being the amazing person you are, and know that heroes everywhere are cheering you on!

With magical wishes and heroic pride,

${heroName} ✨

P.S. - This letter contains a sprinkle of hero magic. Keep it close when you need to remember how brave and special you are!`;
  };

  const letter = generateLetter();

  const handleSendLetter = () => {
    setIsGenerating(true);
    // Simulate letter generation delay
    setTimeout(() => {
      setIsGenerating(false);
      setShowCheckout(true);
    }, 2000);
  };

  if (showCheckout) {
    return (
      <div className="max-w-2xl mx-auto text-center">
        <div className="parchment-card p-12 space-y-6">
          <div className="text-6xl mb-4">📮</div>
          <h2 className="text-3xl font-bold text-foreground mb-4">
            Ready to Send Your Magic Letter!
          </h2>
          <p className="text-lg text-muted-foreground mb-8">
            Your personalized letter from {hero.name} is ready to be printed and mailed to your child.
          </p>
          
          <div className="space-y-4">
            <div className="warm-button inline-flex items-center gap-2 cursor-pointer">
              <Send className="w-5 h-5" />
              Send Letter for $9.99
            </div>
            
            <p className="text-sm text-muted-foreground">
              Includes premium printing and worldwide shipping
            </p>
          </div>
          
          <Button variant="ghost" onClick={onBack} className="mt-6">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Make Changes
          </Button>
        </div>
      </div>
    );
  }

  if (isGenerating) {
    return (
      <div className="max-w-2xl mx-auto text-center">
        <div className="parchment-card p-12">
          <div className="text-6xl mb-6 animate-bounce">{hero.image}</div>
          <h2 className="text-2xl font-bold text-foreground mb-4">
            {hero.name} is writing your letter...
          </h2>
          <div className="flex items-center justify-center gap-2 mb-6">
            <Sparkles className="w-5 h-5 text-primary animate-sparkle" />
            <div className="text-lg text-muted-foreground">Sprinkling magic</div>
            <Sparkles className="w-5 h-5 text-primary animate-sparkle" />
          </div>
          <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
            <div className="h-full bg-gradient-magical animate-shimmer w-1/3"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <Button 
          variant="ghost" 
          onClick={onBack}
          className="hover:bg-muted/50"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Edit
        </Button>
        
        <div className="flex items-center gap-2">
          <div className="text-2xl">{hero.image}</div>
          <div className="text-right">
            <h2 className="text-lg font-bold text-foreground">
              Letter from {hero.name}
            </h2>
            <p className="text-sm text-muted-foreground">
              For {childData.name}
            </p>
          </div>
        </div>
      </div>

      {/* Letter Preview */}
      <div className="relative">
        {/* Parchment Background */}
        <div className="parchment-card p-8 md:p-12">
          <div className="relative z-10">
            {/* Header decoration */}
            <div className="text-center mb-8">
              <div className="flex items-center justify-center gap-2 mb-4">
                <Sparkles className="w-6 h-6 text-primary" />
                <div className="text-3xl">{hero.image}</div>
                <Sparkles className="w-6 h-6 text-primary" />
              </div>
              <div className="h-px bg-gradient-to-r from-transparent via-border to-transparent"></div>
            </div>

            {/* Letter Content */}
            <div className="prose prose-lg max-w-none">
              <div className="whitespace-pre-line text-foreground leading-relaxed font-serif">
                {letter}
              </div>
            </div>

            {/* Footer decoration */}
            <div className="text-center mt-8">
              <div className="h-px bg-gradient-to-r from-transparent via-border to-transparent mb-4"></div>
              <div className="flex items-center justify-center gap-2">
                <Heart className="w-4 h-4 text-accent" />
                <span className="text-sm text-muted-foreground">Sealed with heroic magic</span>
                <Heart className="w-4 h-4 text-accent" />
              </div>
            </div>
          </div>
        </div>

        {/* Magical border effect */}
        <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-primary/20 via-transparent to-accent/20 blur-sm -z-10"></div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-center mt-8">
        <Button
          onClick={handleSendLetter}
          className="magical-button text-lg px-8 py-4"
        >
          <Mail className="w-5 h-5 mr-2" />
          Send This Magic Letter
          <Sparkles className="w-5 h-5 ml-2" />
        </Button>
        
        <Button variant="outline" className="warm-button">
          <Download className="w-4 h-4 mr-2" />
          Download Preview
        </Button>
      </div>

      {/* Info */}
      <div className="text-center mt-6">
        <p className="text-sm text-muted-foreground">
          Your letter will be professionally printed on premium paper and mailed directly to your child
        </p>
      </div>
    </div>
  );
};