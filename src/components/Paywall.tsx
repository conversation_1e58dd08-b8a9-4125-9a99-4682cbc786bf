import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Check, ArrowLeft, Crown, Star, Sparkles } from "lucide-react";
import { toast } from "sonner";

interface PaywallProps {
  onBack: () => void;
  childName: string;
}

export const Paywall = ({ onBack, childName }: PaywallProps) => {
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const plans = [
    {
      id: "tryout",
      name: "One-Time Tryout",
      price: 4.99,
      originalPrice: null,
      description: `Send ${childName} their first magical letter`,
      features: ["1 personalized letter", "Premium paper quality", "Free shipping", "Choose any hero"],
      popular: false,
      savings: null,
      cta: "Send This Letter"
    },
    {
      id: "explorer-monthly",
      name: "Monthly Explorer",
      price: 24.99,
      originalPrice: null,
      description: "4 magical letters per month",
      features: ["4 letters per month", "All heroes available", "Priority shipping", "Achievement tracking", "Cancel anytime"],
      popular: true,
      savings: null,
      cta: "Start Monthly Plan"
    },
    {
      id: "explorer-yearly",
      name: "Explorer (Yearly)",
      price: 21.24,
      originalPrice: 24.99,
      description: "4 letters/month - Save 15%",
      features: ["4 letters per month", "All heroes + seasonal", "Priority shipping", "Achievement tracking", "15% savings", "Cancel anytime"],
      popular: false,
      savings: "Save $44.76/year",
      cta: "Save with Yearly"
    },
    {
      id: "champion-monthly", 
      name: "Monthly Champion",
      price: 49.99,
      originalPrice: null,
      description: "8 magical letters per month",
      features: ["8 letters per month", "All heroes + exclusive", "Express shipping", "Progress reports", "Custom requests"],
      popular: false,
      savings: null,
      cta: "Become Champion"
    },
    {
      id: "champion-yearly",
      name: "Champion (Yearly)",
      price: 42.49,
      originalPrice: 49.99,
      description: "8 letters/month - Save 15%",
      features: ["8 letters per month", "All heroes + exclusive", "Express shipping", "Progress reports", "Custom requests", "15% savings"],
      popular: false,
      savings: "Save $89.88/year",
      cta: "Ultimate Savings"
    }
  ];

  const handlePurchase = async (planId: string) => {
    setIsProcessing(true);
    setSelectedPlan(planId);
    
    // Simulate payment processing
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    toast.success("🎉 Payment successful! Your magical letter is being prepared!");
    setIsProcessing(false);
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <div className="flex items-center justify-center gap-2 mb-4">
          <Crown className="w-8 h-8 text-primary animate-sparkle" />
          <h2 className="text-3xl font-bold text-foreground">
            Send {childName} Their Magical Letter
          </h2>
          <Crown className="w-8 h-8 text-primary animate-sparkle" />
        </div>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Choose how you'd like to continue this magical journey and watch {childName} light up with joy
        </p>
      </div>

      {/* Pricing Cards */}
      <div className="grid gap-6 max-w-6xl mx-auto">
        {/* One-time option */}
        <Card className={`shadow-hero-card border-2 ${selectedPlan === 'tryout' ? 'border-primary' : 'border-border/50'}`}>
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-xl">One-Time Tryout</CardTitle>
                <p className="text-muted-foreground text-sm">Perfect for trying HeroMail</p>
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold text-primary">$4.99</div>
                <p className="text-sm text-muted-foreground">One magical letter</p>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Check className="w-5 h-5 text-primary" />
                <span>Send this letter to {childName}</span>
              </div>
              <Button 
                onClick={() => handlePurchase('tryout')}
                disabled={isProcessing}
                className="min-w-[140px]"
              >
                {isProcessing && selectedPlan === 'tryout' ? (
                  <>
                    <Sparkles className="w-4 h-4 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : (
                  'Send Letter'
                )}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Monthly Plans */}
        <div className="grid md:grid-cols-2 gap-6">
          {plans.slice(1, 3).map((plan) => (
            <Card key={plan.id} className={`shadow-hero-card border-2 ${plan.popular ? 'border-primary' : 'border-border/50'} relative`}>
              {plan.popular && (
                <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-primary text-primary-foreground">
                  <Star className="w-3 h-3 mr-1" />
                  Recommended
                </Badge>
              )}
              <CardHeader className="text-center pb-4">
                <CardTitle className="text-xl">{plan.name}</CardTitle>
                <div className="space-y-1">
                  <div className="flex items-center justify-center gap-2">
                    {plan.originalPrice && (
                      <span className="text-lg text-muted-foreground line-through">${plan.originalPrice}</span>
                    )}
                    <span className="text-3xl font-bold text-primary">${plan.price}</span>
                    <span className="text-sm text-muted-foreground">/month</span>
                  </div>
                  {plan.savings && (
                    <Badge variant="secondary" className="text-xs">{plan.savings}</Badge>
                  )}
                </div>
                <p className="text-sm text-muted-foreground">{plan.description}</p>
              </CardHeader>
              <CardContent className="space-y-4">
                <ul className="space-y-2">
                  {plan.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center gap-2 text-sm">
                      <Check className="w-4 h-4 text-primary" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
                <Button 
                  className="w-full"
                  variant={plan.popular ? "default" : "outline"}
                  onClick={() => handlePurchase(plan.id)}
                  disabled={isProcessing}
                >
                  {isProcessing && selectedPlan === plan.id ? (
                    <>
                      <Sparkles className="w-4 h-4 mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    plan.cta
                  )}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Champion Plans */}
        <div className="grid md:grid-cols-2 gap-6">
          {plans.slice(3).map((plan) => (
            <Card key={plan.id} className="shadow-hero-card border-border/50">
              <CardHeader className="text-center pb-4">
                <CardTitle className="text-xl flex items-center justify-center gap-2">
                  <Crown className="w-5 h-5 text-primary" />
                  {plan.name}
                </CardTitle>
                <div className="space-y-1">
                  <div className="flex items-center justify-center gap-2">
                    {plan.originalPrice && (
                      <span className="text-lg text-muted-foreground line-through">${plan.originalPrice}</span>
                    )}
                    <span className="text-3xl font-bold text-primary">${plan.price}</span>
                    <span className="text-sm text-muted-foreground">/month</span>
                  </div>
                  {plan.savings && (
                    <Badge variant="secondary" className="text-xs">{plan.savings}</Badge>
                  )}
                </div>
                <p className="text-sm text-muted-foreground">{plan.description}</p>
              </CardHeader>
              <CardContent className="space-y-4">
                <ul className="space-y-2">
                  {plan.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center gap-2 text-sm">
                      <Check className="w-4 h-4 text-primary" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
                <Button 
                  className="w-full"
                  variant="outline"
                  onClick={() => handlePurchase(plan.id)}
                  disabled={isProcessing}
                >
                  {isProcessing && selectedPlan === plan.id ? (
                    <>
                      <Sparkles className="w-4 h-4 mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    plan.cta
                  )}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Trust Indicators */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-6 text-sm text-muted-foreground">
          <div className="flex items-center gap-2">
            <Check className="w-4 h-4 text-primary" />
            <span>Secure Payment</span>
          </div>
          <div className="flex items-center gap-2">
            <Check className="w-4 h-4 text-primary" />
            <span>Cancel Anytime</span>
          </div>
          <div className="flex items-center gap-2">
            <Check className="w-4 h-4 text-primary" />
            <span>30-Day Guarantee</span>
          </div>
        </div>
        
        <Button variant="outline" onClick={onBack} className="mt-8">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Letter Preview
        </Button>
      </div>
    </div>
  );
};