@tailwind base;
@tailwind components;
@tailwind utilities;

/* HeroMail Design System - Magical, Premium, Child-Friendly */

@layer base {
  :root {
    /* Magical Color Palette */
    --background: 45 15% 97%;
    --foreground: 240 12% 15%;

    --card: 0 0% 100%;
    --card-foreground: 240 12% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 12% 15%;

    /* Magical Purple Primary */
    --primary: 260 55% 35%;
    --primary-foreground: 0 0% 98%;
    --primary-glow: 260 65% 55%;

    /* Warm Gold Secondary */
    --secondary: 45 85% 65%;
    --secondary-foreground: 30 25% 15%;

    /* Soft Coral Accent */
    --accent: 15 75% 70%;
    --accent-foreground: 240 12% 15%;

    /* Muted Parchment Tones */
    --muted: 45 20% 90%;
    --muted-foreground: 240 8% 50%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 45 15% 85%;
    --input: 45 15% 92%;
    --ring: 260 55% 35%;

    /* Magical Gradients */
    --gradient-magical: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-hero: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--secondary)));
    --gradient-warm: linear-gradient(135deg, hsl(var(--secondary)), hsl(var(--accent)));
    --gradient-subtle: linear-gradient(145deg, hsl(var(--background)), hsl(var(--muted)));

    /* Magical Shadows & Glows */
    --shadow-magical: 0 20px 40px -12px hsl(var(--primary) / 0.25);
    --shadow-hero-card: 0 10px 30px -8px hsl(var(--primary) / 0.15);
    --shadow-glow: 0 0 30px hsl(var(--primary-glow) / 0.3);
    --shadow-warm: 0 8px 25px -8px hsl(var(--secondary) / 0.2);

    /* Animation Variables */
    --transition-magical: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Magical Component Styles */
@layer components {
  .hero-card {
    @apply relative overflow-hidden rounded-xl border border-border/50 bg-card/80 backdrop-blur-sm;
    @apply hover:shadow-[var(--shadow-hero-card)] hover:scale-[1.02] cursor-pointer;
    @apply transition-[var(--transition-magical)];
    @apply before:absolute before:inset-0 before:bg-gradient-to-br before:from-primary/5 before:to-accent/5 before:opacity-0;
    @apply hover:before:opacity-100 before:transition-[var(--transition-smooth)];
  }

  .hero-card:hover {
    box-shadow: var(--shadow-hero-card), var(--shadow-glow);
  }

  .magical-button {
    @apply relative overflow-hidden rounded-lg px-6 py-3 font-semibold text-primary-foreground;
    @apply bg-gradient-to-r from-primary to-primary-glow;
    @apply shadow-[var(--shadow-magical)] hover:shadow-[var(--shadow-glow)];
    @apply transition-[var(--transition-bounce)] hover:scale-105;
    @apply before:absolute before:inset-0 before:bg-gradient-to-r before:from-white/20 before:to-transparent;
    @apply before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-700;
  }

  .warm-button {
    @apply relative overflow-hidden rounded-lg px-6 py-3 font-semibold text-secondary-foreground;
    @apply bg-gradient-to-r from-secondary to-accent;
    @apply shadow-[var(--shadow-warm)] hover:shadow-[var(--shadow-magical)];
    @apply transition-[var(--transition-smooth)] hover:scale-105;
  }

  .parchment-card {
    @apply relative bg-gradient-to-br from-muted/60 to-card;
    @apply border border-border/30 rounded-lg shadow-lg;
    @apply before:absolute before:inset-0 before:bg-gradient-to-br before:from-secondary/5 before:to-accent/5;
    @apply before:rounded-lg before:pointer-events-none;
  }

  .magical-glow {
    @apply animate-pulse;
    box-shadow: 0 0 20px hsl(var(--primary-glow) / 0.4);
  }

  .floating-animation {
    animation: float 3s ease-in-out infinite;
  }

  .sparkle-animation {
    animation: sparkle 2s ease-in-out infinite;
  }
}

/* Magical Animations */
@layer utilities {
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  @keyframes sparkle {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.1); }
  }

  @keyframes magical-shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }

  @keyframes gentle-glow {
    0%, 100% { box-shadow: var(--shadow-hero-card); }
    50% { box-shadow: var(--shadow-glow); }
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-sparkle {
    animation: sparkle 2s ease-in-out infinite;
  }

  .animate-shimmer {
    animation: magical-shimmer 2s infinite;
  }

  .animate-glow {
    animation: gentle-glow 3s ease-in-out infinite;
  }
}